#androidx.activity.ComponentActivityandroid.view.Viewandroid.app.Servicekotlin.Enumandroid.widget.FrameLayout3com.weinuo.quickcommands.model.AdvancedMemoryConfigandroid.os.Parcelable)com.weinuo.quickcommands.model.SharedTask5com.weinuo.quickcommands.model.SharedTriggerCondition*com.weinuo.quickcommands.navigation.Screen%android.app.admin.DeviceAdminReceiver!android.content.BroadcastReceiver1android.accessibilityservice.AccessibilityService8android.service.notification.NotificationListenerService>com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapterAcom.weinuo.quickcommands.storage.adapters.ConditionStorageAdapter<com.weinuo.quickcommands.storage.adapters.TaskStorageAdapter4android.view.GestureDetector.SimpleOnGestureListener>android.view.ScaleGestureDetector.SimpleOnScaleGestureListener2androidx.compose.material3.TopAppBarScrollBehaviorandroidx.lifecycle.ViewModel1com.weinuo.quickcommands.ui.screens.ScreenFactory+androidx.lifecycle.DefaultLifecycleObserver?com.weinuo.quickcommands.ui.theme.system.AnimationConfigurationBcom.weinuo.quickcommands.ui.theme.system.HazeAwareComponentFactoryAcom.weinuo.quickcommands.ui.theme.system.InteractionConfiguration;com.weinuo.quickcommands.ui.theme.system.StyleConfiguration6com.weinuo.quickcommands.ui.theme.system.ThemeProvider9com.weinuo.quickcommands.ui.theme.system.ComponentFactory#android.appwidget.AppWidgetProvider                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       