%<EMAIL><com.weinuo.quickcommands.floating.FloatingAcceleratorServiceHcom.weinuo.quickcommands.floating.FloatingRecordingButton.RecordingState>com.weinuo.quickcommands.floating.FloatingRecordingRequirement:com.weinuo.quickcommands.floating.FloatingRecordingService=com.weinuo.quickcommands.floating.NumberMarkerView.MarkerView>com.weinuo.quickcommands.floating.RecordingOverlay.OverlayViewBcom.weinuo.quickcommands.floating.SwipeEndMarkerView.EndMarkerView2com.weinuo.quickcommands.model.AdvancedGestureType/com.weinuo.quickcommands.model.AdaptiveStrategy?com.weinuo.quickcommands.model.AdvancedMemoryConfig.EventDriven<com.weinuo.quickcommands.model.AdvancedMemoryConfig.Adaptive?com.weinuo.quickcommands.model.AdvancedMemoryConfig.Intelligent:com.weinuo.quickcommands.model.AdvancedMemoryConfig.Hybrid/com.weinuo.quickcommands.model.TemplateCategory*com.weinuo.quickcommands.model.ShareTarget7com.weinuo.quickcommands.model.ShareTargetSelectionMode0com.weinuo.quickcommands.model.SmartReminderType6com.weinuo.quickcommands.model.SmartReminderConfigType-com.weinuo.quickcommands.model.TouchEventType.com.weinuo.quickcommands.model.VolumeOperation4com.weinuo.quickcommands.model.SpeakerphoneOperation0com.weinuo.quickcommands.model.VibrationModeType.com.weinuo.quickcommands.model.VolumePopupType/com.weinuo.quickcommands.model.DoNotDisturbMode/com.weinuo.quickcommands.model.VolumeStreamType)com.weinuo.quickcommands.model.VolumeMode4com.weinuo.quickcommands.model.VolumeAdjustOperation)com.weinuo.quickcommands.model.VolumeTask/com.weinuo.quickcommands.model.DateTimeTaskType1com.weinuo.quickcommands.model.StopwatchOperation-com.weinuo.quickcommands.model.AlarmOperation,com.weinuo.quickcommands.model.AlarmTimeType+com.weinuo.quickcommands.model.DateTimeTask4com.weinuo.quickcommands.model.DeviceActionOperation4com.weinuo.quickcommands.model.FlashlightControlType1com.weinuo.quickcommands.model.StatusBarOperation-com.weinuo.quickcommands.model.TTSAudioStream5com.weinuo.quickcommands.model.DeviceVibrationPattern2com.weinuo.quickcommands.model.SystemOperationType/com.weinuo.quickcommands.model.NotificationTask4com.weinuo.quickcommands.model.NotificationOperation4com.weinuo.quickcommands.model.NotificationSoundType5com.weinuo.quickcommands.model.NotificationActionType3com.weinuo.quickcommands.model.NotificationPriority,com.weinuo.quickcommands.model.ToastDuration4com.weinuo.quickcommands.model.NotificationClearMode6com.weinuo.quickcommands.model.NotificationRestoreMode/com.weinuo.quickcommands.model.DeviceActionTask4com.weinuo.quickcommands.model.ConnectivityOperation/com.weinuo.quickcommands.model.IntentTargetType.com.weinuo.quickcommands.model.IntentParamType.com.weinuo.quickcommands.model.SwitchOperation-com.weinuo.quickcommands.model.PhoneOperation+com.weinuo.quickcommands.model.MakeCallType2com.weinuo.quickcommands.model.AnswerCallDelayType/com.weinuo.quickcommands.model.ClearCallLogType-com.weinuo.quickcommands.model.MediaOperation4com.weinuo.quickcommands.model.MultimediaControlType.com.weinuo.quickcommands.model.MediaButtonType.com.weinuo.quickcommands.model.AudioButtonType0com.weinuo.quickcommands.model.PlayerControlType,com.weinuo.quickcommands.model.SoundPlayType.com.weinuo.quickcommands.model.AudioStreamType.com.weinuo.quickcommands.model.RecordingSource.com.weinuo.quickcommands.model.RecordingFormat4com.weinuo.quickcommands.model.RecordingDurationType5com.weinuo.quickcommands.model.ScreenControlOperation4com.weinuo.quickcommands.model.BrightnessControlType3com.weinuo.quickcommands.model.ScreenOnOffOperation8com.weinuo.quickcommands.model.ScreenOnOffImplementation6com.weinuo.quickcommands.model.ScreenDimnessSensorMode1com.weinuo.quickcommands.model.KeepAwakeOperation2com.weinuo.quickcommands.model.TouchBlockOperation0com.weinuo.quickcommands.model.ForceRotationMode0com.weinuo.quickcommands.model.ScreenTimeoutUnit-com.weinuo.quickcommands.model.CoordinateType,com.weinuo.quickcommands.model.TextMatchMode4com.weinuo.quickcommands.model.AutoClickerSourceType1com.weinuo.quickcommands.model.QuickOperationType/com.weinuo.quickcommands.model.ConnectivityTask(com.weinuo.quickcommands.model.PhoneTask(com.weinuo.quickcommands.model.MediaTask0com.weinuo.quickcommands.model.ScreenControlTask6com.weinuo.quickcommands.model.DeviceSettingsOperation0com.weinuo.quickcommands.model.TriStateOperation2com.weinuo.quickcommands.model.SystemSettingsTable6com.weinuo.quickcommands.model.SystemSettingsValueType,com.weinuo.quickcommands.model.WallpaperType0com.weinuo.quickcommands.model.WallpaperLocation0com.weinuo.quickcommands.model.ImmersiveModeType1com.weinuo.quickcommands.model.AmbientDisplayMode1com.weinuo.quickcommands.model.DeviceSettingsTask0com.weinuo.quickcommands.model.LocationOperation2com.weinuo.quickcommands.model.LocationShareMethod;com.weinuo.quickcommands.model.LocationServiceControlMethod:com.weinuo.quickcommands.model.LocationUpdateFrequencyUnit+com.weinuo.quickcommands.model.LocationTask3com.weinuo.quickcommands.model.ApplicationOperation1com.weinuo.quickcommands.model.ShellExecutionMode-com.weinuo.quickcommands.model.ForceStopScope-com.weinuo.quickcommands.model.GroupCheckMode.com.weinuo.quickcommands.model.CleanupRuleType,com.weinuo.quickcommands.model.AppImportance1com.weinuo.quickcommands.model.AppSortingStrategy3com.weinuo.quickcommands.model.MemoryCheckFrequency6com.weinuo.quickcommands.model.ConditionCheckFrequency.com.weinuo.quickcommands.model.MemoryCheckMode-com.weinuo.quickcommands.model.UsageTimeRange1com.weinuo.quickcommands.model.UsageFrequencyMode1com.weinuo.quickcommands.model.SimpleStrategyType.com.weinuo.quickcommands.model.ApplicationTask3com.weinuo.quickcommands.model.InformationOperation/com.weinuo.quickcommands.model.SimCardSelection.com.weinuo.quickcommands.model.InformationTask,com.weinuo.quickcommands.model.FileOperation,com.weinuo.quickcommands.model.FileWriteMode0com.weinuo.quickcommands.model.FileOperationType0com.weinuo.quickcommands.model.FileSelectionMode2com.weinuo.quickcommands.model.CompressionLocation0com.weinuo.quickcommands.model.CompressionFormat/com.weinuo.quickcommands.model.CompressionLevel0com.weinuo.quickcommands.model.FileOperationTask.com.weinuo.quickcommands.model.CameraOperation)com.weinuo.quickcommands.model.CameraType6com.weinuo.quickcommands.model.VideoRecordingOperation+com.weinuo.quickcommands.model.SaveLocation)com.weinuo.quickcommands.model.CameraTask-com.weinuo.quickcommands.model.TimeRepeatMode2com.weinuo.quickcommands.model.ScheduledRepeatMode(com.weinuo.quickcommands.model.DayOfWeek/com.weinuo.quickcommands.model.TimeIntervalUnit0com.weinuo.quickcommands.model.TimeConditionType+com.weinuo.quickcommands.model.SunEventType0com.weinuo.quickcommands.model.ManualTriggerType5com.weinuo.quickcommands.model.FingerprintGestureType+com.weinuo.quickcommands.model.MediaKeyType/com.weinuo.quickcommands.model.SwipeStartCorner-com.weinuo.quickcommands.model.SwipeDirection/com.weinuo.quickcommands.model.VolumeButtonType3com.weinuo.quickcommands.model.BatteryConditionType2com.weinuo.quickcommands.model.BatteryLevelSubType.com.weinuo.quickcommands.model.ChargingSubType1com.weinuo.quickcommands.model.TemperatureSubType1com.weinuo.quickcommands.model.PowerButtonSubType3com.weinuo.quickcommands.model.PowerSaveModeSubType4com.weinuo.quickcommands.model.BatteryStateCondition-com.weinuo.quickcommands.model.ConnectionType0com.weinuo.quickcommands.model.ConnectionSubType7com.weinuo.quickcommands.model.ConnectionStateCondition.com.weinuo.quickcommands.model.SensorStateType.com.weinuo.quickcommands.model.DeviceEventType+com.weinuo.quickcommands.model.GpsStateType/com.weinuo.quickcommands.model.LogcatBufferType.com.weinuo.quickcommands.model.ScreenEventType,com.weinuo.quickcommands.model.DockStateType/com.weinuo.quickcommands.model.SimCardStateType1com.weinuo.quickcommands.model.DarkThemeStateType0com.weinuo.quickcommands.model.SystemSettingType0com.weinuo.quickcommands.model.AutoSyncStateType4com.weinuo.quickcommands.model.NotificationEventType;com.weinuo.quickcommands.model.NotificationAppSelectionMode9com.weinuo.quickcommands.model.NotificationAppIncludeMode;com.weinuo.quickcommands.model.NotificationContentMatchMode4com.weinuo.quickcommands.model.NotificationSoundMode-com.weinuo.quickcommands.model.RingerModeType0com.weinuo.quickcommands.model.MusicPlaybackType/com.weinuo.quickcommands.model.AirplaneModeType.com.weinuo.quickcommands.model.MemoryStateType1com.weinuo.quickcommands.model.LightThresholdType.com.weinuo.quickcommands.model.OrientationType/com.weinuo.quickcommands.model.SensitivityLevel-com.weinuo.quickcommands.model.SleepStateType'com.weinuo.quickcommands.model.FlipType,com.weinuo.quickcommands.model.ProximityType+com.weinuo.quickcommands.model.ActivityType3com.weinuo.quickcommands.model.DeviceEventCondition3com.weinuo.quickcommands.model.SensorStateCondition1com.weinuo.quickcommands.model.TimeBasedCondition+com.weinuo.quickcommands.model.AppStateType3com.weinuo.quickcommands.model.AppStateCategoryType/com.weinuo.quickcommands.model.AppDetectionMode2com.weinuo.quickcommands.model.AppStateTriggerMode7com.weinuo.quickcommands.model.ScreenContentTriggerMode7com.weinuo.quickcommands.model.InterfaceInteractionType5com.weinuo.quickcommands.model.ScreenContentMatchType0com.weinuo.quickcommands.model.AppStateCondition5com.weinuo.quickcommands.model.CommunicationStateType0com.weinuo.quickcommands.model.ContactFilterType0com.weinuo.quickcommands.model.ContactFilterMode:com.weinuo.quickcommands.model.CommunicationStateCondition5com.weinuo.quickcommands.model.ManualTriggerCondition9com.weinuo.quickcommands.model.TaskerLocaleConditionState8com.weinuo.quickcommands.navigation.Screen.QuickCommands9com.weinuo.quickcommands.navigation.Screen.GlobalSettings7com.weinuo.quickcommands.navigation.Screen.PhoneCheckup9com.weinuo.quickcommands.navigation.Screen.SmartReminders;com.weinuo.quickcommands.navigation.Screen.CommandTemplates;com.weinuo.quickcommands.navigation.Screen.QuickCommandForm?<EMAIL>;<EMAIL><com.weinuo.quickcommands.navigation.Screen.RingtoneSelection?com.weinuo.quickcommands.navigation.Screen.AdvancedMemoryConfig=com.weinuo.quickcommands.navigation.Screen.MemoryLearningData;com.weinuo.quickcommands.navigation.Screen.AccountSelection=com.weinuo.quickcommands.navigation.Screen.StopwatchSelection?com.weinuo.quickcommands.navigation.Screen.ShareTargetSelectionBcom.weinuo.quickcommands.navigation.Screen.AppImportanceManagementBcom.weinuo.quickcommands.navigation.Screen.AdvancedCleanupStrategy9com.weinuo.quickcommands.navigation.Screen.AddCleanupRule?com.weinuo.quickcommands.navigation.Screen.GestureRecordingEditDcom.weinuo.quickcommands.navigation.Screen.SmartReminderDetailConfigGcom.weinuo.quickcommands.navigation.Screen.CustomShoppingPlatformConfigBcom.weinuo.quickcommands.navigation.Screen.CustomAppPlatformConfig7com.weinuo.quickcommands.permission.DeviceAdminReceiverJcom.weinuo.quickcommands.permission.GlobalPermissionManager.PermissionType;com.weinuo.quickcommands.permission.GlobalSettingsOperation?com.weinuo.quickcommands.permission.ShellScriptShizukuOperation:<EMAIL>?com.weinuo.quickcommands.service.ClipboardRefreshOverlayService6com.weinuo.quickcommands.service.FloatingButtonServiceGcom.weinuo.quickcommands.service.GestureRecognitionAccessibilityServiceIcom.weinuo.quickcommands.service.InterfaceInteractionAccessibilityServiceAcom.weinuo.quickcommands.service.QuickCommandsNotificationService5com.weinuo.quickcommands.service.QuickCommandsService<com.weinuo.quickcommands.service.SmartReminderOverlayServiceDcom.weinuo.quickcommands.service.SystemOperationAccessibilityServiceAcom.weinuo.quickcommands.service.SystemPriorityEnhancementService9com.weinuo.quickcommands.service.TouchBlockOverlayService>com.weinuo.quickcommands.shortcut.QuickCommandExecutorActivity?com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivityBcom.weinuo.quickcommands.smartreminder.AddressDetector.AddressTypeVcom.weinuo.quickcommands.smartreminder.ScreenRotationReminderHandler.DeviceOrientation.com.weinuo.quickcommands.storage.StorageDomain,<EMAIL>>com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter;com.weinuo.quickcommands.storage.adapters.CameraTaskAdapterLcom.weinuo.quickcommands.storage.adapters.CommunicationStateConditionAdapterIcom.weinuo.quickcommands.storage.adapters.ConnectionStateConditionAdapterAcom.weinuo.quickcommands.storage.adapters.ConnectivityTaskAdapter=<EMAIL>=com.weinuo.quickcommands.storage.adapters.LocationTaskAdapterGcom.weinuo.quickcommands.storage.adapters.ManualTriggerConditionAdapter:com.weinuo.quickcommands.storage.adapters.MediaTaskAdapterAcom.weinuo.quickcommands.storage.adapters.NotificationTaskAdapter:com.weinuo.quickcommands.storage.adapters.PhoneTaskAdapterBcom.weinuo.quickcommands.storage.adapters.ScreenControlTaskAdapterEcom.weinuo.quickcommands.storage.adapters.SensorStateConditionAdapterCcom.weinuo.quickcommands.storage.adapters.TimeBasedConditionAdapter;com.weinuo.quickcommands.storage.adapters.VolumeTaskAdapter*com.weinuo.quickcommands.ui.BubbleActivity=com.weinuo.quickcommands.ui.activities.AddCleanupRuleActivityFcom.weinuo.quickcommands.ui.activities.AdvancedCleanupStrategyActivityCcom.weinuo.quickcommands.ui.activities.AdvancedMemoryConfigActivity;com.weinuo.quickcommands.ui.activities.AppSelectionActivity?com.weinuo.quickcommands.ui.activities.ContactSelectionActivityDcom.weinuo.quickcommands.ui.activities.DetailedConfigurationActivity<com.weinuo.quickcommands.ui.activities.IconSelectionActivityAcom.weinuo.quickcommands.ui.activities.MemoryLearningDataActivity?<EMAIL>:com.weinuo.quickcommands.ui.components.CircleCropImageViewJcom.weinuo.quickcommands.ui.components.CircleCropImageView.GestureListenerOcom.weinuo.quickcommands.ui.components.CircleCropImageView.ScaleGestureListener<com.weinuo.quickcommands.ui.components.WaterBallMaterialType7com.weinuo.quickcommands.ui.components.ArcAnimationType=com.weinuo.quickcommands.ui.components.ParticleAnimationStateScom.weinuo.quickcommands.ui.components.integrated.IntegratedTopAppBarScrollBehavior;<EMAIL>>com.weinuo.quickcommands.ui.recording.GestureRecordingActivityBcom.weinuo.quickcommands.ui.recording.GestureRecordingEditActivityCcom.weinuo.quickcommands.ui.recording.GestureRecordingEditViewModel3com.weinuo.quickcommands.ui.recording.RecordingMode?com.weinuo.quickcommands.ui.recording.GestureRecordingViewModel8com.weinuo.quickcommands.ui.screens.AccountSelectionMode4com.weinuo.quickcommands.ui.screens.AppSelectionMode7com.weinuo.quickcommands.ui.screens.GroupSelectionState8com.weinuo.quickcommands.ui.screens.ContactSelectionMode=com.weinuo.quickcommands.ui.screens.ContactGroupSelectionMode9com.weinuo.quickcommands.ui.screens.RingtoneSelectionMode.com.weinuo.quickcommands.ui.screens.ScreenType:com.weinuo.quickcommands.ui.screens.StopwatchSelectionModeDcom.weinuo.quickcommands.ui.screens.oceanblue.OceanBlueScreenFactory?<EMAIL>?<EMAIL>?<EMAIL>@com.weinuo.quickcommands.ui.theme.skyblue.ComponentAnimationType<com.weinuo.quickcommands.ui.theme.skyblue.PageTransitionType3com.weinuo.quickcommands.ui.theme.skyblue.IconLevelAcom.weinuo.quickcommands.ui.theme.skyblue.SkyBlueComponentFactoryIcom.weinuo.quickcommands.ui.theme.skyblue.SkyBlueInteractionConfigurationBcom.weinuo.quickcommands.ui.theme.skyblue.ComponentInteractionTypeCcom.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration7com.weinuo.quickcommands.ui.theme.skyblue.ComponentType>com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueThemeProvider1com.weinuo.quickcommands.ui.theme.system.AppThemeBcom.weinuo.quickcommands.ui.theme.system.HazeAwareComponentFactory6com.weinuo.quickcommands.ui.theme.system.ComponentType7com.weinuo.quickcommands.ui.theme.system.DesignApproach9com.weinuo.quickcommands.ui.theme.system.InteractionState6com.weinuo.quickcommands.ui.theme.system.AnimationType5com.weinuo.quickcommands.ui.theme.system.ThemeFeatureFcom.weinuo.quickcommands.utils.ExperimentalFeatureDetector.ClickTarget:com.weinuo.quickcommands.utils.RingtoneHelper.RingtoneInfo:com.weinuo.quickcommands.utils.RingtoneHelper.RingtoneType=com.weinuo.quickcommands.utils.VibrationManager.VibrationModeBcom.weinuo.quickcommands.utils.VibrationManager.VibrationIntensity5com.weinuo.quickcommands.viewmodel.CheckupButtonState8com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel6com.weinuo.quickcommands.widget.OneClickCommandWidget16com.weinuo.quickcommands.widget.OneClickCommandWidget26com.weinuo.quickcommands.widget.OneClickCommandWidget36com.weinuo.quickcommands.widget.OneClickCommandWidget4:com.weinuo.quickcommands.widget.WidgetClickHandlerActivity                                                                                                                                                                                                                                                                                                                                                                                                 