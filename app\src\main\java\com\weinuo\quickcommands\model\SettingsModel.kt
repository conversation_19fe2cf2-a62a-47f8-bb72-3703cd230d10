package com.weinuo.quickcommands.model

/**
 * 网络状态数据类
 * 用于保存和恢复网络状态
 *
 * @property wifiEnabled WiFi是否启用
 * @property mobileDataEnabled 移动数据是否启用
 * @property saveTime 保存时间戳（毫秒）
 */
data class NetworkState(
    val wifiEnabled: Boolean = false,
    val mobileDataEnabled: Boolean = false,
    val saveTime: Long = System.currentTimeMillis()
) {
    /**
     * 检查网络状态是否过期
     * @param timeoutHours 超时时间（小时），默认24小时
     * @return 是否过期
     */
    fun isExpired(timeoutHours: Int = 24): Bo<PERSON>an {
        val currentTime = System.currentTimeMillis()
        val timeoutMillis = timeoutHours * 60 * 60 * 1000L
        return (currentTime - saveTime) > timeoutMillis
    }
}

/**
 * 全局设置数据模型（精简版 - 仅保留快捷指令需要的设置）
 *
 * @property widgetUpdateEnabled 小组件更新开关（默认关闭以节省资源）
 * @property widgetUpdateInterval 小组件更新间隔（小时，默认24小时）
 * @property savedNetworkState 保存的网络状态（用于快捷指令网络状态恢复）
 * @property experimentalFeaturesEnabled 实验性功能是否启用（临时状态，应用退出后重置）
 * @property experimentalFeaturesUnlocked 实验性功能是否已解锁（持久状态）
 * @property appLanguage 应用语言设置（"system"=系统默认, "zh"=中文, "en"=英文，默认系统默认）
 * @property searchFieldPlaceholderFontWeight 搜索框提示文字字重（"medium"=中等, "regular"=常规，默认中等）
 * @property searchFieldIconWeight 搜索框图标粗细（"regular"=常规体, "medium"=中黑体, "bold"=粗体，默认中黑体）
 * @property topAppBarHeight 标题栏高度（dp，默认55dp）
 * @property topAppBarTitleFontSize 标题栏标题字体大小（sp，默认25sp）
 * @property topAppBarTitleFontWeight 标题栏标题字重（"normal"=常规, "medium"=中等, "bold"=粗体，默认粗体）
 * @property topAppBarTitleVerticalOffset 标题栏标题垂直偏移量（dp，正值向下，负值向上，默认0dp）
 * @property topAppBarTitleHorizontalOffset 标题栏标题水平偏移量（dp，正值向右，负值向左，默认0dp）
 * @property topAppBarType 标题栏类型（"standard"=标准顶部应用栏, "collapsible"=可折叠顶部应用栏，默认标准）
 *
 * 卡片样式设置（天空蓝主题专用）：
 * @property cardCornerRadius 卡片圆角大小（dp，默认20dp）
 * @property cardDefaultHorizontalPadding 卡片默认水平内边距（dp，默认6dp）
 * @property cardDefaultVerticalPadding 卡片默认垂直内边距（dp，默认6dp）
 * @property cardCompactHorizontalPadding 卡片紧凑水平内边距（dp，默认4dp）
 * @property cardCompactVerticalPadding 卡片紧凑垂直内边距（dp，默认4dp）
 * @property cardLargeHorizontalPadding 卡片大水平内边距（dp，默认12dp）
 * @property cardLargeVerticalPadding 卡片大垂直内边距（dp，默认12dp）
 * @property cardItemSpacing 卡片项目间距（dp，默认8dp）
 * @property cardSectionSpacing 卡片区域间距（dp，默认20dp）
 * @property cardContentVerticalSpacing 卡片内容垂直间距（dp，默认16dp）
 * @property cardContentHorizontalSpacing 卡片内容水平间距（dp，默认12dp）
 * @property cardSelectedElevation 卡片选中阴影（dp，默认0dp）
 * @property cardSelectedBorderWidth 卡片选中边框宽度（dp，默认3dp）
 *
 * 页面布局设置（天空蓝主题专用）：
 * @property pageContentHorizontalPadding 页面内容水平边距（dp，默认16dp）
 * @property pageSearchFieldMargin 搜索框外边距（dp，默认16dp）
 * @property pageHeaderSpacing 页面标题间距（dp，默认16dp）
 * @property pageBottomPadding 页面底部边距（dp，默认88dp）
 * @property pageScrollContentSpacing 滚动内容间距（dp，默认8dp）
 */
data class GlobalSettings(
    var widgetUpdateEnabled: Boolean = false,
    var widgetUpdateInterval: Int = 24,
    var savedNetworkState: NetworkState? = null,
    var experimentalFeaturesEnabled: Boolean = false,
    var experimentalFeaturesUnlocked: Boolean = false,
    var appLanguage: String = "system",
    var searchFieldPlaceholderFontWeight: String = "medium",
    var searchFieldIconWeight: String = "medium",
    var topAppBarHeight: Int = 55,
    var topAppBarTitleFontSize: Int = 25,
    var topAppBarTitleFontWeight: String = "bold",
    var topAppBarTitleVerticalOffset: Int = 0,
    var topAppBarTitleHorizontalOffset: Int = 0,
    var topAppBarType: String = "standard",
    var screenTitleFontSize: Int = 20,               // 界面标题字体大小（sp）- 各个界面的标题
    var formSectionTitleFontSize: Int = 17,          // 表单标题字体大小（sp）- 新建/编辑界面中各个区块标题

    // 卡片样式设置（天空蓝主题专用）- 使用当前代码中的默认值
    var cardCornerRadius: Int = 20,              // 对应 defaultCornerRadius = 20.dp
    var cardDefaultHorizontalPadding: Int = 13,  // 对应 defaultHorizontalPadding = 13.dp
    var cardDefaultVerticalPadding: Int = 14,    // 对应 defaultVerticalPadding = 14.dp（内容卡片专用）
    var cardSettingsVerticalPadding: Int = 6,    // 对应 settingsVerticalPadding = 6.dp
    var cardCompactHorizontalPadding: Int = 11,  // 对应 compactHorizontalPadding = 11.dp
    var cardCompactVerticalPadding: Int = 4,     // 对应 compactVerticalPadding = 4.dp
    var cardLargeHorizontalPadding: Int = 19,    // 对应 largeHorizontalPadding = 19.dp
    var cardLargeVerticalPadding: Int = 12,      // 对应 largeVerticalPadding = 12.dp
    var cardItemSpacing: Int = 8,                // 对应 itemSpacing = 8.dp（页面中卡片之间的间距）
    var cardSectionSpacing: Int = 20,            // 对应 sectionSpacing = 20.dp
    var cardContentVerticalSpacing: Int = 0,     // 对应 contentVerticalSpacing = 0.dp
    var cardContentHorizontalSpacing: Int = 12,  // 对应 contentHorizontalSpacing = 12.dp
    var cardSelectedElevation: Int = 0,          // 对应 selectedElevation = 0.dp
    var cardSelectedBorderWidth: Int = 3,        // 对应 selectedBorderWidth = 3.dp
    var cardTitleFontSize: Int = 15,             // 卡片标题字体大小（sp）- 指令名称/模板标题
    var cardTitleFontWeight: String = "medium",  // 卡片标题字重（"normal"=常规, "medium"=中等, "bold"=粗体，默认中等）
    var cardContentFontSize: Int = 13,           // 卡片内容字体大小（sp）- 功能描述
    var cardContentFontWeight: String = "medium", // 卡片描述字重（"normal"=常规, "medium"=中等, "bold"=粗体，默认中等）
    var cardIconSize: Int = 48,                  // 快捷指令卡片图标大小（dp）

    // 页面布局设置（天空蓝主题专用）- 使用当前代码中的默认值
    var pageContentHorizontalPadding: Int = 16,  // 页面内容水平边距
    var pageSearchFieldMargin: Int = 16,         // 搜索框外边距
    var pageHeaderSpacing: Int = 16,             // 页面标题间距
    var pageBottomPadding: Int = 88,             // 页面底部边距（为底部导航栏留空间）
    var pageScrollContentSpacing: Int = 8,       // 滚动内容间距

    // UI间距设置（天空蓝主题专用）
    var uiSettingsItemVerticalPadding: Int = 12,    // 设置项垂直间距
    var uiDividerHorizontalPadding: Int = 0,        // 分割线水平间距
    var uiSettingsCardPadding: Int = 16,            // 设置卡片内边距
    var uiSettingsItemSpacing: Int = 16,            // 设置项之间的间距
    var uiSettingsTitleSpacing: Int = 8,            // 设置标题与内容的间距
    var uiSettingsDescriptionSpacing: Int = 4,      // 设置描述与标题的间距
    var uiSettingsGroupTitleHorizontalPadding: Int = 13, // 设置分组标题水平间距
    var uiSettingsGroupTitleTopPadding: Int = 30,   // 设置分组标题上边距
    var uiSettingsGroupTitleBottomPadding: Int = 6, // 设置分组标题下边距
    var uiGlobalSettingsItemSpacing: Int = 0,       // 全局设置界面专用间距
    var uiDividerVisible: Boolean = true,           // 分割线是否显示

    // 用户体验设置
    var checkupResultDelayEnabled: Boolean = false,   // 体检结果延迟显示开关
    var checkupDisplayDurationSeconds: Int = 5,       // 结果显示延迟时长（秒）
    var fixedCheckupScoreEnabled: Boolean = false,    // 固定体检分数开关
    var fixedCheckupScore: Int = 100,                 // 固定体检分数值

    // 水球高级材质设置（天空蓝主题专用）
    var waterBallAdvancedMaterialEnabled: Boolean = false, // 水球高级材质开关
    var waterWaveReserveSpaceEnabled: Boolean = false,     // 水波动效预留空间开关
    var waterWaveStopThreshold: Int = 90,                  // 水位停止阈值（分数）

    // 悬浮加速球设置
    var floatingAcceleratorEnabled: Boolean = false,       // 悬浮加速球开关

    // 对话框间距设置（天空蓝主题专用）
    var dialogOuterPadding: Int = 12,               // 对话框外边距（dp）
    var dialogIconBottomPadding: Int = 16,          // 图标下方间距（dp）
    var dialogTitleBottomPadding: Int = 16,         // 标题下方间距（dp）
    var dialogContentBottomPadding: Int = 4,        // 内容下方间距（dp）
    var dialogContentVerticalPadding: Int = 8,      // 内容区域垂直间距（dp）
    var dialogInputBottomPadding: Int = 8,          // 输入框下方间距（dp）
    var dialogButtonTopPadding: Int = 2,            // 按钮上方间距（dp）
    var dialogButtonBottomPadding: Int = 14,        // 按钮下方间距（dp）
    var dialogTitleFontSize: Int = 17,              // 对话框标题字体大小（sp）
    var dialogDividerHorizontalPadding: Int = 12,   // 对话框分割线水平间距（dp）

    // 天空蓝主题颜色配置 - 使用当前代码中的默认颜色值
    // 主要颜色系统
    var skyBluePrimary: String = "0xFF0A59F7",              // brand - 品牌色
    var skyBlueOnPrimary: String = "0xFFFFFFFF",            // font_on_primary - 一级文本反色
    var skyBluePrimaryContainer: String = "0x330A59F7",     // comp_emphasize_secondary - 20%高亮背景
    var skyBlueOnPrimaryContainer: String = "0xE5000000",   // font_primary - 一级文本

    // 次要颜色系统
    var skyBlueSecondary: String = "0x99000000",            // font_secondary - 二级文本
    var skyBlueOnSecondary: String = "0x99FFFFFF",          // font_on_secondary - 二级文本反色
    var skyBlueSecondaryContainer: String = "0xFFF1F3F5",   // background_secondary - 二级背景
    var skyBlueOnSecondaryContainer: String = "0x99000000", // font_secondary - 二级文本

    // 第三颜色系统
    var skyBlueTertiary: String = "0x66000000",             // font_tertiary - 三级文本
    var skyBlueOnTertiary: String = "0x66FFFFFF",           // font_on_tertiary - 三级文本反色
    var skyBlueTertiaryContainer: String = "0xFFE5E5EA",    // background_tertiary - 三级背景
    var skyBlueOnTertiaryContainer: String = "0x66000000",  // font_tertiary - 三级文本

    // 错误颜色系统
    var skyBlueError: String = "0xFFE84026",                // warning - 一级警示色
    var skyBlueOnError: String = "0xFFFFFFFF",              // font_on_primary - 一级文本反色
    var skyBlueErrorContainer: String = "0xFFED6F21",       // alert - 二级警示色
    var skyBlueOnErrorContainer: String = "0xFFFFFFFF",     // font_on_primary - 一级文本反色

    // 表面颜色系统
    var skyBlueBackground: String = "0xFFF1F3F5",           // background_secondary - 内容背景
    var skyBlueOnBackground: String = "0xE5000000",         // font_primary - 一级文本
    var skyBlueSurface: String = "0xFFF1F3F5",              // comp_background_gray - 导航栏背景
    var skyBlueOnSurface: String = "0xE5000000",            // font_primary - 一级文本
    var skyBlueSurfaceVariant: String = "0xFFF1F3F5",       // comp_background_gray - 灰色背景
    var skyBlueOnSurfaceVariant: String = "0x99000000",     // font_secondary - 二级文本

    // 扩展颜色
    var skyBlueConfirm: String = "0xFF64BB5C",              // confirm - 确认色
    var skyBlueFontEmphasize: String = "0xFF0A59F7",        // font_emphasize - 高亮文本
    var skyBlueIconEmphasize: String = "0xFF0A59F7",        // icon_emphasize - 高亮图标
    var skyBlueIconSubEmphasize: String = "0x660A59F7",     // icon_sub_emphasize - 高亮辅助图标
    var skyBlueBackgroundEmphasize: String = "0xFF0A59F7",  // background_emphasize - 高亮背景
    var skyBlueBackgroundFocus: String = "0xFFF1F3F5",      // comp_background_focus - 获焦态背景色

    // 底部导航栏颜色配置
    var skyBlueBottomNavBackground: String = "0xFFF1F3F5",   // 底部导航栏背景色（默认使用surface色）
    var skyBlueBottomNavSelectedIcon: String = "0xFF0A59F7", // 底部导航栏选中图标颜色（默认使用primary色）
    var skyBlueBottomNavUnselectedIcon: String = "0x99000000", // 底部导航栏未选中图标颜色（默认使用onSurfaceVariant色）

    // 标题栏颜色配置
    var skyBlueTopBarBackground: String = "0xFFF1F3F5",      // 标题栏背景色（默认与底部导航栏相同）

    // 底部导航栏尺寸配置（天空蓝主题专用）- 使用当前代码中的默认值
    var bottomNavHeight: Int = 80,                    // 底部导航栏高度（dp，默认80dp）
    var bottomNavHorizontalPadding: Int = 16,         // 底部导航栏水平内边距（dp，默认16dp）
    var bottomNavVerticalPadding: Int = 7,            // 底部导航栏垂直内边距（dp，默认7dp）
    var bottomNavItemCornerRadius: Int = 16,          // 导航项圆角大小（dp，默认16dp）
    var bottomNavItemOuterPadding: Int = 4,           // 导航项外边距（dp，默认4dp）
    var bottomNavItemVerticalPadding: Int = 8,        // 导航项垂直内边距（dp，默认8dp）
    var bottomNavItemHorizontalPadding: Int = 12,     // 导航项水平内边距（dp，默认12dp）
    var bottomNavIconSize: Int = 24,                  // 图标大小（dp，默认24dp）
    var bottomNavIconTextSpacing: Int = 4,            // 图标与文字间距（dp，默认4dp）
    var bottomNavTextFontSize: Int = 11,              // 文字字体大小（sp，默认11sp，对应labelSmall）
    var bottomNavSelectedFontWeight: String = "medium", // 选中状态字重（"normal"=常规, "medium"=中等, "bold"=粗体，默认中等）
    var bottomNavUnselectedFontWeight: String = "normal", // 未选中状态字重（"normal"=常规, "medium"=中等, "bold"=粗体，默认常规）
    var bottomNavColorAnimationDuration: Int = 150,   // 颜色动画时长（ms，默认150ms）
    var bottomNavBackgroundAnimationDuration: Int = 150, // 背景动画时长（ms，默认150ms）
    var bottomNavItemArrangement: String = "spaceEvenly", // 导航项排列方式（"spaceEvenly"=均匀分布, "spaceBetween"=两端对齐, "spaceAround"=环绕分布，默认均匀分布）

    // 对话框样式设置（天空蓝主题专用）
    var dialogBlurEnabled: Boolean = true,            // 对话框模糊效果开关（默认开启）
    var dialogCornerRadius: Int = 28,                 // 对话框圆角大小（dp，默认28dp，与Material 3 AlertDialog保持一致）
    var dialogBlurIntensity: Float = 0.6f,            // 对话框模糊强度（0.0-1.0，默认0.6）

    // 顶部应用栏按钮样式设置（天空蓝主题专用）
    var topAppBarButtonCircleBackgroundEnabled: Boolean = true, // 顶部应用栏按钮半透明圆形背景开关（默认开启）
    var topAppBarButtonCircleBackgroundSize: Int = 40,          // 圆形背景大小（dp，默认40dp）
    var topAppBarButtonCircleBackgroundHorizontalMargin: Int = 28, // 圆形背景距离屏幕左右边缘的缩进（dp，默认28dp）
    var topAppBarButtonCircleBackgroundRightMargin: Int = 3,    // 返回按钮右边缘到标题左边缘的距离（dp，默认3dp）

    // 导航项显示控制设置（天空蓝主题专用）
    var navigationItemsVisibility: Map<String, Boolean> = mapOf(
        "phone_checkup" to true,      // 体检导航项显示状态（默认显示）
        "quick_commands" to true,     // 指令导航项显示状态（默认显示）
        "command_templates" to true,  // 探索导航项显示状态（默认显示）
        "smart_reminders" to true,    // 提醒导航项显示状态（默认显示）
        "global_settings" to true     // 设置导航项显示状态（默认显示）
    ),

    // 默认启动页面设置（天空蓝主题专用）
    var defaultStartupPage: String = "phone_checkup"  // 默认启动页面（默认为体检页面）
)


