O Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktK Japp/src/main/java/com/weinuo/quickcommands/floating/AcceleratorBallView.ktK Japp/src/main/java/com/weinuo/quickcommands/floating/AcceleratorBallView.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/IOSStyleTimePicker.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/IOSStyleTimePicker.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/IOSStyleTimePicker.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/IOSStyleTimePicker.ktD Capp/src/main/java/com/weinuo/quickcommands/navigation/Navigation.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.kt; :app/src/main/java/com/weinuo/quickcommands/MainActivity.ktF Eapp/src/main/java/com/weinuo/quickcommands/data/SettingsRepository.ktB Aapp/src/main/java/com/weinuo/quickcommands/model/SettingsModel.ktL Kapp/src/main/java/com/weinuo/quickcommands/navigation/ThemedBottomNavBar.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueGlobalSettingsScreen.ktO Napp/src/main/java/com/weinuo/quickcommands/execution/SharedExecutionHandler.ktK Japp/src/main/java/com/weinuo/quickcommands/floating/AcceleratorBallView.ktO Napp/src/main/java/com/weinuo/quickcommands/floating/FloatingAcceleratorBall.ktR Qapp/src/main/java/com/weinuo/quickcommands/floating/FloatingAcceleratorManager.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/activities/IconSelectionActivity.ktK Japp/src/main/java/com/weinuo/quickcommands/ui/components/ShizukuTipCard.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/SkipOptionsComponents.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.kta `app/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedSearchTextField.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedTopAppBar.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueCardButton.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueTopAppBarButton.kt] \app/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedCommandTemplateCard.ktf eapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedCommandTemplateCardWithImage.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedQuickCommandCard.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedSmartReminderCard.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/configuration/ConfigurationDataProvider.ktN Mapp/src/main/java/com/weinuo/quickcommands/ui/screens/AddCleanupRuleScreen.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/screens/AdvancedCleanupStrategyScreen.ktL Kapp/src/main/java/com/weinuo/quickcommands/ui/screens/AppSelectionScreen.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/screens/ContactSelectionScreen.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailConfigurationScreen.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailedConfigurationScreen.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/screens/QuickCommandFormScreen.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/screens/UnifiedConfigurationScreen.kta `app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueGlobalSettingsScreen.kt` _app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueQuickCommandsScreen.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueCommandTemplatesScreen.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBluePhoneCheckupScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueQuickCommandsScreen.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueSmartRemindersScreen.kti happ/src/main/java/com/weinuo/quickcommands/ui/theme/manager/BottomNavigationStyleConfigurationManager.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/CardStyleConfigurationManager.kta `app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/DialogSpacingConfigurationManager.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/PageLayoutConfigurationManager.kt` _app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/SkyBlueColorConfigurationManager.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/UISpacingConfigurationManager.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueDialogConfigManager.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueStyleConfiguration.ktG Fapp/src/main/java/com/weinuo/quickcommands/utils/AppLanguageManager.ktN Mapp/src/main/java/com/weinuo/quickcommands/viewmodel/PhoneCheckupViewModel.kt; :app/src/main/java/com/weinuo/quickcommands/MainActivity.ktF Eapp/src/main/java/com/weinuo/quickcommands/data/SettingsRepository.ktB Aapp/src/main/java/com/weinuo/quickcommands/model/SettingsModel.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueGlobalSettingsScreen.ktO Napp/src/main/java/com/weinuo/quickcommands/execution/SharedExecutionHandler.ktK Japp/src/main/java/com/weinuo/quickcommands/floating/AcceleratorBallView.ktO Napp/src/main/java/com/weinuo/quickcommands/floating/FloatingAcceleratorBall.ktR Qapp/src/main/java/com/weinuo/quickcommands/floating/FloatingAcceleratorManager.ktL Kapp/src/main/java/com/weinuo/quickcommands/navigation/ThemedBottomNavBar.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/activities/IconSelectionActivity.ktK Japp/src/main/java/com/weinuo/quickcommands/ui/components/ShizukuTipCard.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/SkipOptionsComponents.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.kta `app/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedSearchTextField.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedTopAppBar.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueCardButton.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueTopAppBarButton.kt] \app/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedCommandTemplateCard.ktf eapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedCommandTemplateCardWithImage.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedQuickCommandCard.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedSmartReminderCard.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/configuration/ConfigurationDataProvider.ktN Mapp/src/main/java/com/weinuo/quickcommands/ui/screens/AddCleanupRuleScreen.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/screens/AdvancedCleanupStrategyScreen.ktL Kapp/src/main/java/com/weinuo/quickcommands/ui/screens/AppSelectionScreen.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/screens/ContactSelectionScreen.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailConfigurationScreen.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailedConfigurationScreen.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/screens/QuickCommandFormScreen.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/screens/UnifiedConfigurationScreen.kta `app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueGlobalSettingsScreen.kt` _app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueQuickCommandsScreen.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueCommandTemplatesScreen.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBluePhoneCheckupScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueQuickCommandsScreen.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueSmartRemindersScreen.kti happ/src/main/java/com/weinuo/quickcommands/ui/theme/manager/BottomNavigationStyleConfigurationManager.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/CardStyleConfigurationManager.kta `app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/DialogSpacingConfigurationManager.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/PageLayoutConfigurationManager.kt` _app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/SkyBlueColorConfigurationManager.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/UISpacingConfigurationManager.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueDialogConfigManager.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueStyleConfiguration.ktG Fapp/src/main/java/com/weinuo/quickcommands/utils/AppLanguageManager.ktN Mapp/src/main/java/com/weinuo/quickcommands/viewmodel/PhoneCheckupViewModel.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueGlobalSettingsScreen.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueGlobalSettingsScreen.kt; :app/src/main/java/com/weinuo/quickcommands/MainActivity.ktF Eapp/src/main/java/com/weinuo/quickcommands/data/SettingsRepository.ktB Aapp/src/main/java/com/weinuo/quickcommands/model/SettingsModel.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueGlobalSettingsScreen.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBluePhoneCheckupScreen.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBluePhoneCheckupScreen.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBluePhoneCheckupScreen.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBluePhoneCheckupScreen.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBluePhoneCheckupScreen.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBluePhoneCheckupScreen.kt