package com.weinuo.quickcommands.ui.screens.skyblue

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.AppRepository
import com.weinuo.quickcommands.data.PhoneCheckupRepository
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.execution.SharedExecutionHandler
import com.weinuo.quickcommands.ui.components.ShizukuTipCardWithPermission
import com.weinuo.quickcommands.ui.components.WaterBallComponent
import com.weinuo.quickcommands.ui.components.StaticWaterBallComponent
import com.weinuo.quickcommands.ui.components.ArcAnimationType
import com.weinuo.quickcommands.ui.components.ParticleAnimationState
import com.weinuo.quickcommands.ui.components.integrated.SetIntegratedTopAppBar
import com.weinuo.quickcommands.ui.components.integrated.rememberIntegratedTopAppBarScrollBehavior
import com.weinuo.quickcommands.ui.effects.backgroundBlurEffect
import com.weinuo.quickcommands.ui.effects.HazeManager
import com.weinuo.quickcommands.ui.theme.config.BlurComponent
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands.ui.theme.manager.BlurConfigurationManager
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel
import com.weinuo.quickcommands.viewmodel.CheckupButtonState
import dev.chrisbanes.haze.materials.HazeMaterials

/**
 * 天空蓝主题的手机体检界面
 * 
 * 特点：
 * - 整合设计风格
 * - 支持模糊效果
 * - 统一的视觉体验
 * - 现代化的扁平设计
 */
@Composable
fun SkyBluePhoneCheckupScreen(
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current
    val hazeManager = remember { HazeManager.getInstance(context) }
    val hazeState = hazeManager.globalHazeState

    // 创建依赖项
    val appRepository = remember { AppRepository(context) }
    val sharedExecutionHandler = remember { SharedExecutionHandler(context) }
    val repository = remember { PhoneCheckupRepository(context, appRepository, sharedExecutionHandler) }
    val viewModel = remember { PhoneCheckupViewModel(repository, context) }

    // 获取设置和配置
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 收集状态
    val healthScore by viewModel.healthScore.collectAsStateWithLifecycle()
    val runningAppsCount by viewModel.runningAppsCount.collectAsStateWithLifecycle()
    val buttonState by viewModel.buttonState.collectAsStateWithLifecycle()
    val isOptimizing by viewModel.isOptimizing.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val lastOptimizeResult by viewModel.lastOptimizeResult.collectAsStateWithLifecycle()
    val errorMessage by viewModel.errorMessage.collectAsStateWithLifecycle()
    val showShizukuTip by viewModel.showShizukuTipCard.collectAsStateWithLifecycle()
    val particleAnimationState by viewModel.particleAnimationState.collectAsStateWithLifecycle()

    // 响应式状态文字
    val healthStatusDescription by viewModel.healthStatusDescription.collectAsStateWithLifecycle()
    val secondaryStatusText by viewModel.secondaryStatusText.collectAsStateWithLifecycle()

    // 注意：不需要手动刷新，ViewModel会在初始化时自动开始体检

    // 计算TopAppBar高度，用于内容的初始顶部padding
    val density = LocalDensity.current
    val statusBarHeight = with(density) { WindowInsets.statusBars.getTop(density).toDp() }
    val topAppBarHeight = globalSettings.topAppBarHeight.dp + statusBarHeight // StandardTopAppBar高度

    // 创建LazyColumn滚动状态
    val lazyListState = rememberLazyListState()

    // 创建滚动行为 - 支持可折叠标题栏，只有在内容可滚动时才允许标题栏折叠
    val scrollBehavior = rememberIntegratedTopAppBarScrollBehavior(
        canScroll = {
            // 检查LazyColumn是否可以滚动
            lazyListState.canScrollBackward || lazyListState.canScrollForward
        }
    )

    // 配置TopAppBar - 支持可折叠类型
    SetIntegratedTopAppBar(
        config = TopAppBarConfig(
            title = stringResource(R.string.phone_checkup_title),
            style = TopAppBarStyle.STANDARD,
            scrollBehavior = scrollBehavior, // 关键：添加滚动行为
            windowInsets = WindowInsets.statusBars
        )
    )

    // 动态计算顶部padding - 根据标题栏类型优化内容定位
    val topPadding = remember(globalSettings.topAppBarType, statusBarHeight, topAppBarHeight) {
        if (globalSettings.topAppBarType == "collapsible") {
            // 可折叠模式：使用展开状态高度，确保内容不被遮挡
            152.dp + statusBarHeight
        } else {
            // 标准模式：使用固定高度
            topAppBarHeight
        }
    }

    // 获取屏幕配置信息
    val configuration = LocalConfiguration.current

    // 计算自适应尺寸
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp

    // 根据屏幕高度动态计算水球大小
    val waterBallSize = remember(screenHeight) {
        when {
            screenHeight < 600.dp -> minOf(screenWidth * 0.4f, 160.dp)
            screenHeight < 700.dp -> minOf(screenWidth * 0.45f, 180.dp)
            screenHeight < 800.dp -> minOf(screenWidth * 0.5f, 200.dp)
            else -> minOf(screenWidth * 0.55f, 240.dp)
        }
    }

    // 根据屏幕高度动态计算间距
    val sectionSpacing = if (screenHeight < 600.dp) 16.dp else 24.dp
    val waterBallPadding = if (screenHeight < 600.dp) 12.dp else 16.dp

    // 获取页面布局配置（天空蓝主题专用）
    val pageLayoutConfig = SkyBlueStyleConfiguration.getDynamicPageLayoutConfig(settingsRepository)
    val cardStyleConfig = SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)

    LazyColumn(
        state = lazyListState,
        modifier = modifier
            .fillMaxSize()
            .nestedScroll(scrollBehavior.nestedScrollConnection)
            .padding(horizontal = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        contentPadding = PaddingValues(
            top = topPadding, // 动态计算的顶部padding
            bottom = pageLayoutConfig.bottomPadding // 使用动态配置的底部边距
        ),
        verticalArrangement = Arrangement.spacedBy(cardStyleConfig.itemSpacing)
    ) {
        // Shizuku提示卡片
        item {
            ShizukuTipCardWithPermission(
                visible = showShizukuTip,
                onDismiss = { viewModel.dismissShizukuTipCard() },
                modifier = Modifier.padding(bottom = if (showShizukuTip) 16.dp else 0.dp)
            )
        }

        // 水球组件 - 直接显示，无背景
        item {
            when (buttonState) {
                CheckupButtonState.FIRST_TIME -> {
                    // 首次进入状态使用静态水球
                    StaticWaterBallComponent(
                        size = waterBallSize,
                        showGrid = true, // 天空蓝主题的首次体检界面显示网格
                        modifier = Modifier.padding(waterBallPadding).padding(bottom = sectionSpacing)
                    )
                }
                else -> {
                    // 所有其他状态使用统一的动态水球，避免重新创建导致粒子重新初始化
                    WaterBallComponent(
                        score = healthScore,
                        size = waterBallSize,
                        isAnimating = when (buttonState) {
                            CheckupButtonState.MANUAL_CHECKING,
                            CheckupButtonState.OPTIMIZING -> true
                            else -> !isLoading
                        },
                        arcType = when {
                            // 旋转弧线在处理状态和等待展示状态时显示
                            particleAnimationState == ParticleAnimationState.PROCESSING ||
                            particleAnimationState == ParticleAnimationState.WAITING_DISPLAY -> ArcAnimationType.ROTATING
                            // 静态进度弧线在静止状态时显示（除了首次体检状态和权限状态）
                            particleAnimationState == ParticleAnimationState.NORMAL &&
                            buttonState !in listOf(
                                CheckupButtonState.FIRST_TIME,
                                CheckupButtonState.PERMISSION_NEEDED
                            ) -> ArcAnimationType.STATIC_PROGRESS
                            // 其他状态（收缩、膨胀）不显示弧线
                            else -> ArcAnimationType.NONE
                        },
                        showInternalBubbles = when (buttonState) {
                            CheckupButtonState.MANUAL_CHECKING,
                            CheckupButtonState.OPTIMIZING -> true
                            else -> false
                        },
                        particleAnimationState = particleAnimationState,
                        isProcessing = particleAnimationState != ParticleAnimationState.NORMAL,
                        onParticleAnimationStateChange = { newState ->
                            viewModel.updateParticleAnimationState(newState)
                        },
                        modifier = Modifier
                            .padding(waterBallPadding)
                            .padding(bottom = sectionSpacing)
                    )
                }
            }
        }

        // 状态信息 - 使用模糊背景
        item {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .backgroundBlurEffect(
                        hazeState = hazeState,
                        style = HazeMaterials.thin(),
                        component = BlurComponent.DIALOG
                    )
                    .padding(20.dp)
                    .padding(bottom = sectionSpacing),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = healthStatusDescription,
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        color = themeContext.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = secondaryStatusText,
                        style = MaterialTheme.typography.bodyLarge,
                        color = themeContext.colorScheme.onSurface.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }

        // 操作按钮区域 - 体检时和优化时隐藏
        if (buttonState != CheckupButtonState.MANUAL_CHECKING &&
            buttonState != CheckupButtonState.OPTIMIZING) {
            item {
                SkyBlueOptimizeButton(
                    isOptimizing = isOptimizing,
                    canOptimize = viewModel.canClick(),
                    buttonText = viewModel.getButtonText(),
                    onOptimize = { viewModel.handleButtonClick() },
                    hazeState = hazeState,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }
        }


    }
}

/**
 * 天空蓝风格的优化按钮
 */
@Composable
private fun SkyBlueOptimizeButton(
    isOptimizing: Boolean,
    canOptimize: Boolean,
    buttonText: String,
    onOptimize: () -> Unit,
    hazeState: dev.chrisbanes.haze.HazeState,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    Box(
        modifier = modifier
            .fillMaxWidth(0.7f)
            .backgroundBlurEffect(
                hazeState = hazeState,
                style = HazeMaterials.thin(),
                component = BlurComponent.DIALOG
            )
    ) {
        Button(
            onClick = onOptimize,
            enabled = canOptimize && !isOptimizing,
            colors = ButtonDefaults.buttonColors(
                containerColor = themeContext.colorScheme.primary, // 使用天空蓝主题品牌色
                contentColor = Color.White,
                disabledContainerColor = themeContext.colorScheme.primary.copy(alpha = 0.5f),
                disabledContentColor = Color.White.copy(alpha = 0.7f)
            ),
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp),
            shape = RoundedCornerShape(24.dp)
        ) {
            if (isOptimizing) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = buttonText,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            } else {
                Text(
                    text = buttonText,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 天空蓝风格的权限设置卡片
 */
@Composable
private fun SkyBluePermissionCard(
    missingPermissions: List<String>,
    onRefreshPermissions: () -> Unit,
    hazeState: dev.chrisbanes.haze.HazeState,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current

    Box(
        modifier = modifier
            .fillMaxWidth()
            .backgroundBlurEffect(
                hazeState = hazeState,
                style = HazeMaterials.thin(),
                component = BlurComponent.DIALOG
            )
            .padding(20.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "需要开启权限",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = themeContext.colorScheme.error
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "缺少以下权限：${missingPermissions.joinToString("、")}",
                style = MaterialTheme.typography.bodyMedium,
                color = themeContext.colorScheme.onSurface.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = onRefreshPermissions,
                colors = ButtonDefaults.buttonColors(
                    containerColor = themeContext.colorScheme.error,
                    contentColor = themeContext.colorScheme.onError
                ),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text("重新检查权限")
            }
        }
    }
}




